const pdfService = require('./services/pdfService');
const airlineLogoService = require('./services/airlineLogoService');

/**
 * Test logo retrieval for international airlines
 */
async function testInternationalLogoRetrieval() {
  console.log('🎨 INTERNATIONAL AIRLINE LOGO RETRIEVAL TEST');
  console.log('='.repeat(60));

  // Test representative airlines from each region
  const testAirlines = [
    // From screenshot
    { name: 'AIR FRANCE', region: 'Screenshot' },
    { name: 'THAI', region: 'Screenshot' },
    
    // Asian representatives
    { name: 'KOREAN AIR', region: 'Asian' },
    { name: 'THAI AIRWAYS', region: 'Asian' },
    { name: 'MALAYSI<PERSON> AIRLINES', region: 'Asian' },
    { name: 'CHINA EASTERN', region: 'Asian' },
    
    // Middle Eastern representatives
    { name: 'ETIHAD AIRWAYS', region: 'Middle Eastern' },
    { name: 'SAUDIA', region: 'Middle Eastern' },
    { name: 'GU<PERSON> AIR', region: 'Middle Eastern' },
    
    // African representatives
    { name: 'ETHIOPIAN AIRLINES', region: 'African' },
    { name: 'KENYA AIRWAYS', region: 'African' },
    { name: 'EGYPTAIR', region: 'African' },
    
    // South American representatives
    { name: 'LATAM', region: 'South American' },
    { name: 'AVIANCA', region: 'South American' },
    
    // Oceania representatives
    { name: 'QANTAS', region: 'Oceania' },
    { name: 'AIR NEW ZEALAND', region: 'Oceania' }
  ];

  const results = [];
  let successCount = 0;

  console.log('Testing logo retrieval for representative international airlines:\n');

  for (const airline of testAirlines) {
    const airlineCode = pdfService.getAirlineCode(airline.name);
    
    try {
      console.log(`🔍 Testing ${airline.name} (${airlineCode})...`);
      
      const logo = await airlineLogoService.getLogoForPDF(airlineCode, airline.name);
      
      const isBase64PNG = logo.startsWith('data:image/png;base64,');
      const isSVG = logo.startsWith('data:image/svg');
      const isURL = logo.startsWith('http');
      
      const success = isBase64PNG;
      if (success) successCount++;
      
      const result = {
        airline: airline.name,
        region: airline.region,
        code: airlineCode,
        type: isBase64PNG ? 'Authentic PNG' : isSVG ? 'Generated SVG' : isURL ? 'External URL' : 'Unknown',
        size: logo.length,
        success
      };
      
      results.push(result);
      
      const status = isBase64PNG ? '✅' : isSVG ? '⚠️' : '❌';
      console.log(`  ${status} ${result.type} (${result.size} chars)`);
      
    } catch (error) {
      const result = {
        airline: airline.name,
        region: airline.region,
        code: airlineCode,
        type: 'ERROR',
        size: 0,
        success: false,
        error: error.message
      };
      
      results.push(result);
      console.log(`  ❌ ERROR: ${error.message}`);
    }
    
    console.log(''); // Empty line for readability
  }

  // Summary by region
  console.log('📊 RESULTS BY REGION:');
  const regionStats = {};
  
  results.forEach(result => {
    if (!regionStats[result.region]) {
      regionStats[result.region] = { total: 0, success: 0 };
    }
    regionStats[result.region].total++;
    if (result.success) regionStats[result.region].success++;
  });
  
  Object.entries(regionStats).forEach(([region, stats]) => {
    const percentage = Math.round(stats.success / stats.total * 100);
    console.log(`  ${region}: ${stats.success}/${stats.total} (${percentage}%)`);
  });

  // Overall summary
  const overallPercentage = Math.round(successCount / testAirlines.length * 100);
  console.log(`\n🎯 OVERALL SUCCESS RATE: ${successCount}/${testAirlines.length} (${overallPercentage}%)`);

  // Detailed results
  console.log('\n📋 DETAILED RESULTS:');
  results.forEach(result => {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    console.log(`  ${status} ${result.airline} (${result.code}) → ${result.type}`);
  });

  // Failed airlines
  const failedAirlines = results.filter(r => !r.success);
  if (failedAirlines.length > 0) {
    console.log('\n⚠️ AIRLINES NEEDING ATTENTION:');
    failedAirlines.forEach(airline => {
      console.log(`  - ${airline.airline} (${airline.code}): ${airline.type}`);
      if (airline.error) {
        console.log(`    Error: ${airline.error}`);
      }
    });
  } else {
    console.log('\n🎉 ALL INTERNATIONAL AIRLINES PASSED!');
    console.log('All tested airlines are getting authentic PNG logos.');
  }

  return { results, successRate: overallPercentage };
}

// Run the test
testInternationalLogoRetrieval()
  .then(({ results, successRate }) => {
    console.log(`\n🏁 INTERNATIONAL LOGO RETRIEVAL TEST COMPLETE!`);
    console.log(`📊 Success Rate: ${successRate}%`);
    
    if (successRate >= 80) {
      console.log('🎯 EXCELLENT: International airline logo system is working well!');
    } else if (successRate >= 60) {
      console.log('⚠️ GOOD: Most international airlines working, some may need attention.');
    } else {
      console.log('🚨 NEEDS WORK: Many international airlines still falling back to SVG logos.');
    }
  })
  .catch(error => {
    console.error('\n❌ International logo retrieval test failed:', error);
    process.exit(1);
  });
