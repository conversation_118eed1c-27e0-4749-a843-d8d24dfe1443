import React, { useState, useEffect } from 'react'
import axios from 'axios'

function App() {
  const [backendStatus, setBackendStatus] = useState('checking')
  const [bookingRef, setBookingRef] = useState('CATHAY001')
  const [passengers, setPassengers] = useState([
    { firstName: 'JANE', lastName: 'COOPER' },
    { firstName: 'JENNY', lastName: 'WILSON' }
  ])
  const [result, setResult] = useState('')
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    checkBackendStatus()
  }, [])

  const checkBackendStatus = async () => {
    try {
      const response = await axios.get('/api/health')
      setBackendStatus('online')
    } catch (error) {
      setBackendStatus('offline')
    }
  }

  const generatePDF = async () => {
    setLoading(true)
    setResult('')
    
    try {
      const requestData = {
        bookingReference: bookingRef,
        passengers: passengers.filter(p => p.firstName && p.lastName),
        outboundFlight: {
          airline: 'CATHAY PACIFIC',
          flightNumber: 'CX 784',
          departureDate: '2021-07-18',
          departureTime: '16:05',
          arrivalTime: '21:05',
          duration: '05hr(s) 00min(s)',
          aircraft: 'AIRBUS INDUSTRIE A330-300',
          departureAirport: {
            iata: 'DPS',
            city: 'Denpasar-Bali',
            country: 'Indonesia'
          },
          arrivalAirport: {
            iata: 'HKG',
            city: 'Hong Kong',
            country: 'Hong Kong'
          }
        },
        returnFlight: {
          airline: 'CATHAY PACIFIC',
          flightNumber: 'CX 844',
          departureDate: '2021-07-19',
          departureTime: '02:05',
          arrivalTime: '06:00',
          duration: '15hr(s) 55min(s)',
          aircraft: 'BOEING 777-300ER',
          departureAirport: {
            iata: 'HKG',
            city: 'Hong Kong',
            country: 'Hong Kong'
          },
          arrivalAirport: {
            iata: 'JFK',
            city: 'New York',
            country: 'United States Of America'
          }
        }
      }

      const response = await axios.post('/api/tickets/generate', requestData)
      
      if (response.data.success) {
        setResult(`✅ PDF Generated! Booking: ${bookingRef}`)
      } else {
        setResult(`❌ Error: ${response.data.error}`)
      }
    } catch (error) {
      setResult(`❌ Request failed: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const downloadPDF = () => {
    alert('⚠️ PDF generation temporarily disabled. Please check back later.')
  }

  return (
    <div style={{ 
      minHeight: '100vh', 
      padding: '2rem',
      color: 'white'
    }}>
      {/* Header */}
      <header style={{
        background: 'rgba(13, 27, 42, 0.95)',
        padding: '1rem 2rem',
        borderRadius: '15px',
        marginBottom: '2rem',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div>
          <h1 style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>VerifiedOnward</h1>
          <p style={{ opacity: 0.8, fontSize: '0.9rem' }}>Embassy-Approved Flight Reservations</p>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <div style={{
            width: '8px',
            height: '8px',
            borderRadius: '50%',
            background: backendStatus === 'online' ? '#22c55e' : '#ef4444'
          }}></div>
          <span style={{ fontSize: '0.9rem' }}>
            Backend {backendStatus === 'online' ? 'Online' : 'Offline'}
          </span>
        </div>
      </header>

      {/* Main Content */}
      <div style={{ maxWidth: '800px', margin: '0 auto' }}>
        <div style={{
          textAlign: 'center',
          marginBottom: '3rem'
        }}>
          <h2 style={{ fontSize: '2.5rem', marginBottom: '1rem' }}>
            🎯 Embassy-Ready Flight Reservations
          </h2>
          <p style={{ fontSize: '1.2rem', opacity: 0.9 }}>
            Generate professional Cathay Pacific-style flight documents in 60 seconds
          </p>
        </div>

        {/* Form Card */}
        <div style={{
          background: 'white',
          color: '#333',
          padding: '2rem',
          borderRadius: '20px',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          marginBottom: '2rem'
        }}>
          <h3 style={{ marginBottom: '1.5rem', color: '#0D1B2A' }}>
            🎫 Generate Flight Reservation
          </h3>

          <div style={{ marginBottom: '1rem' }}>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
              Booking Reference:
            </label>
            <input
              type="text"
              value={bookingRef}
              onChange={(e) => setBookingRef(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '2px solid #e5e7eb',
                borderRadius: '10px',
                fontSize: '1rem'
              }}
            />
          </div>

          {passengers.map((passenger, index) => (
            <div key={index} style={{ 
              display: 'grid', 
              gridTemplateColumns: '1fr 1fr', 
              gap: '1rem',
              marginBottom: '1rem'
            }}>
              <div>
                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
                  Passenger {index + 1} - First Name:
                </label>
                <input
                  type="text"
                  value={passenger.firstName}
                  onChange={(e) => {
                    const newPassengers = [...passengers]
                    newPassengers[index].firstName = e.target.value
                    setPassengers(newPassengers)
                  }}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e5e7eb',
                    borderRadius: '10px',
                    fontSize: '1rem'
                  }}
                />
              </div>
              <div>
                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
                  Passenger {index + 1} - Last Name:
                </label>
                <input
                  type="text"
                  value={passenger.lastName}
                  onChange={(e) => {
                    const newPassengers = [...passengers]
                    newPassengers[index].lastName = e.target.value
                    setPassengers(newPassengers)
                  }}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e5e7eb',
                    borderRadius: '10px',
                    fontSize: '1rem'
                  }}
                />
              </div>
            </div>
          ))}

          <div style={{ 
            display: 'flex', 
            gap: '1rem', 
            justifyContent: 'center',
            marginTop: '2rem'
          }}>
            <button
              onClick={generatePDF}
              disabled={loading}
              style={{
                background: 'linear-gradient(135deg, #6366F1, #7C3AED)',
                color: 'white',
                padding: '0.75rem 2rem',
                border: 'none',
                borderRadius: '50px',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: loading ? 'not-allowed' : 'pointer',
                opacity: loading ? 0.7 : 1
              }}
            >
              {loading ? '🔄 Generating...' : '🎯 Generate Cathay Pacific PDF'}
            </button>
            
            <button
              onClick={downloadPDF}
              style={{
                background: 'linear-gradient(135deg, #64748b, #475569)',
                color: 'white',
                padding: '0.75rem 2rem',
                border: 'none',
                borderRadius: '50px',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer'
              }}
            >
              📥 Download PDF
            </button>
          </div>

          {result && (
            <div style={{
              marginTop: '1.5rem',
              padding: '1rem',
              borderRadius: '10px',
              background: result.includes('✅') ? '#f0fdf4' : '#fef2f2',
              border: `2px solid ${result.includes('✅') ? '#22c55e' : '#ef4444'}`,
              color: result.includes('✅') ? '#16a34a' : '#dc2626'
            }}>
              {result}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default App
