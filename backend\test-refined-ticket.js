const pdfService = require('./services/pdfService');
const { generateReservationCodes } = require('./utils/reservationCodeGenerator');

async function testRefinedTicket() {
  console.log('🧪 Testing refined ticket generation...');
  
  // Test cases with different airlines
  const testCases = [
    {
      name: 'Ryanair Round Trip',
      airline: 'RYANAIR',
      airlineCode: 'FR',
      segments: 2
    },
    {
      name: 'EasyJet One Way',
      airline: 'EASYJET',
      airlineCode: 'U2',
      segments: 1
    },
    {
      name: 'British Airways Round Trip',
      airline: 'BRITISH AIRWAYS',
      airlineCode: 'BA',
      segments: 2
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n✈️  Testing ${testCase.name}...`);
    
    // Generate short reservation codes
    const codes = generateReservationCodes(testCase.airlineCode);
    console.log(`📋 Generated codes: ${codes.reservationCode}, ${codes.airlineReservationCode}`);
    
    // Create segments
    const segments = [];
    
    // First segment (DEPARTURE)
    segments.push({
      airline: testCase.airline,
      flightNo: `${testCase.airlineCode}${Math.floor(Math.random() * 9000) + 1000}`,
      from: {
        code: 'LHR',
        city: 'London, United Kingdom',
        time: '14:30',
        terminal: '2'
      },
      to: {
        code: 'BCN',
        city: 'Barcelona, Spain',
        time: '17:45',
        terminal: '1'
      },
      departureDay: 'MONDAY 15 JAN',
      duration: '2h 15m',
      flightClass: 'Economy Class (M)',
      aircraft: 'BOEING 737-800',
      distance: '712 miles',
      stops: '0',
      meals: 'Available'
    });
    
    // Second segment (RETURN) if round trip
    if (testCase.segments === 2) {
      segments.push({
        airline: testCase.airline,
        flightNo: `${testCase.airlineCode}${Math.floor(Math.random() * 9000) + 1000}`,
        from: {
          code: 'BCN',
          city: 'Barcelona, Spain',
          time: '18:20',
          terminal: '1'
        },
        to: {
          code: 'LHR',
          city: 'London, United Kingdom',
          time: '19:35',
          terminal: '2'
        },
        departureDay: 'FRIDAY 19 JAN',
        duration: '2h 15m',
        flightClass: 'Economy Class (M)',
        aircraft: 'BOEING 737-800',
        distance: '712 miles',
        stops: '0',
        meals: 'Available'
      });
    }
    
    const ticketData = {
      tripDates: testCase.segments === 2 ? '15 JAN 2025 • 19 JAN 2025' : '15 JAN 2025',
      destination: testCase.segments === 2 ? 'ROUND TRIP RESERVATION' : 'ONE-WAY FLIGHT RESERVATION',
      passengers: [
        { name: 'SMITH/JOHN' },
        { name: 'SMITH/JANE' }
      ],
      reservationCode: codes.reservationCode,
      airlineReservationCode: codes.airlineReservationCode,
      segments,
      showNotice: true,
      customNotice: "This is not a valid boarding pass. Please check with the airline before departure."
    };
    
    const outputPath = `./test-outputs/refined-ticket-${testCase.name.toLowerCase().replace(/\s+/g, '-')}.pdf`;
    
    try {
      await pdfService.generatePDF(ticketData, outputPath);
      console.log(`✅ Generated: ${outputPath}`);
      
      // Verify key features
      console.log(`   📝 Reservation Code: ${codes.reservationCode} (${codes.reservationCode.length} chars)`);
      console.log(`   📝 Airline Code: ${codes.airlineReservationCode} (${codes.airlineReservationCode.length} chars)`);
      console.log(`   👥 Passengers: ${ticketData.passengers.map(p => p.name).join(', ')}`);
      console.log(`   ✈️  Segments: ${segments.length} (${segments.map((s, i) => i === 0 ? 'DEPARTURE' : 'RETURN').join(', ')})`);
      
    } catch (error) {
      console.error(`❌ Error generating ${testCase.name}:`, error.message);
    }
  }
  
  await pdfService.cleanup();
  console.log('\n🎉 Refined ticket testing completed!');
}

// Run the test
testRefinedTicket().catch(console.error);
