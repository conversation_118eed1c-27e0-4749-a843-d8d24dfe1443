const pdfService = require('./services/pdfService');

/**
 * Analyze current international airline coverage
 */
function analyzeInternationalCoverage() {
  console.log('🌍 INTERNATIONAL AIRLINE COVERAGE ANALYSIS');
  console.log('='.repeat(60));

  // Test airlines from the screenshot and major international carriers
  const testAirlines = {
    'Screenshot Airlines': [
      'AIR FRANCE',
      'THAI'
    ],
    'Asian Airlines': [
      'SINGAPORE AIRLINES',
      'CATHAY PACIFIC', 
      'JAPAN AIRLINES',
      'KOREAN AIR',
      'THAI AIRWAYS',
      'MALAYSIA AIRLINES',
      'PH<PERSON><PERSON><PERSON>NE AIRLINES',
      'CHINA AIRLINES',
      'CHINA EASTERN',
      'CHINA SOUTHERN',
      'ASIANA AIRLINES',
      'VIETNAM AIRLINES',
      'GARUDA INDONESIA',
      'CEBU PACIFIC',
      'JETSTAR ASIA'
    ],
    'African Airlines': [
      'ETHIOPIAN AIRLINES',
      'SOUTH AFRICAN AIRWAYS',
      'KENYA AIRWAYS',
      'EGYPTAIR',
      'ROYAL AIR MAROC',
      'TUNISAIR',
      'AIR ALGERIE',
      'RWANDAIR',
      'FASTJET'
    ],
    'Middle Eastern Airlines': [
      'EMIRATES',
      'QATAR AIRWAYS',
      'ETIHAD AIRWAYS',
      'TURKISH AIRLINES',
      'SAUDIA',
      'GULF AIR',
      'OMAN AIR',
      'FLYDUBAI',
      'AIR ARABIA'
    ],
    'South American Airlines': [
      'LATAM',
      'AVIANCA',
      'COPA AIRLINES',
      'GOL',
      'AZUL',
      'SKY AIRLINE',
      'VIVA AIR'
    ],
    'Oceania Airlines': [
      'QANTAS',
      'JETSTAR',
      'AIR NEW ZEALAND',
      'VIRGIN AUSTRALIA',
      'TIGERAIR'
    ]
  };

  const allMissing = [];
  const allMapped = [];
  
  Object.entries(testAirlines).forEach(([region, airlines]) => {
    console.log(`\n🌏 ${region}:`);
    
    const regionMissing = [];
    const regionMapped = [];
    
    airlines.forEach(airline => {
      const code = pdfService.getAirlineCode(airline);
      if (code === 'XX') {
        regionMissing.push(airline);
        allMissing.push({ region, airline });
        console.log(`  ❌ ${airline} → XX (MISSING)`);
      } else {
        regionMapped.push({ airline, code });
        allMapped.push({ region, airline, code });
        console.log(`  ✅ ${airline} → ${code}`);
      }
    });
    
    const regionCoverage = Math.round(regionMapped.length / airlines.length * 100);
    console.log(`  📊 Coverage: ${regionMapped.length}/${airlines.length} (${regionCoverage}%)`);
  });

  // Overall summary
  const totalAirlines = allMissing.length + allMapped.length;
  const overallCoverage = Math.round(allMapped.length / totalAirlines * 100);
  
  console.log('\n📊 OVERALL COVERAGE SUMMARY:');
  console.log(`✅ Mapped: ${allMapped.length}/${totalAirlines} (${overallCoverage}%)`);
  console.log(`❌ Missing: ${allMissing.length}/${totalAirlines} (${100 - overallCoverage}%)`);

  console.log('\n🚨 MISSING AIRLINES BY REGION:');
  const missingByRegion = {};
  allMissing.forEach(({ region, airline }) => {
    if (!missingByRegion[region]) missingByRegion[region] = [];
    missingByRegion[region].push(airline);
  });
  
  Object.entries(missingByRegion).forEach(([region, airlines]) => {
    console.log(`\n  ${region}:`);
    airlines.forEach(airline => console.log(`    - ${airline}`));
  });

  return { allMissing, allMapped, overallCoverage };
}

// Run the analysis
const results = analyzeInternationalCoverage();

console.log('\n🎯 NEXT STEPS:');
console.log('1. Add missing IATA code mappings to pdfService.js');
console.log('2. Add logo sources to airlineLogoService.js');
console.log('3. Test logo retrieval for international carriers');
console.log('4. Generate test PDF with international airlines');

module.exports = { analyzeInternationalCoverage };
