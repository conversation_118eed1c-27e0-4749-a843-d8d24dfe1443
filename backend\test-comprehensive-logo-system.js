const airlineLogoService = require('./services/airlineLogoService');
const pdfService = require('./services/pdfService');

async function testComprehensiveLogoSystem() {
  console.log('🧪 Testing Comprehensive Airline Logo System...\n');

  // Test 1: Verify coverage after improvements
  console.log('1. Testing airline coverage after improvements:');
  const result = await runCoverageTest();
  console.log(`   ✅ Coverage: ${result.coverage}% (${result.supported}/${result.total} airlines)\n`);

  // Test 2: Test Ethiopian Airlines specifically
  console.log('2. Testing Ethiopian Airlines (primary focus):');
  await testSpecificAirline('ET', 'Ethiopian Airlines');

  // Test 3: Test newly added airlines
  console.log('3. Testing newly added airlines:');
  const newAirlines = [
    { code: 'CX', name: 'Cathay Pacific' },
    { code: 'JL', name: 'Japan Airlines' },
    { code: 'TP', name: 'TAP Air Portugal' },
    { code: 'A<PERSON>', name: '<PERSON><PERSON><PERSON>' },
    { code: 'S<PERSON>', name: 'Aeroflot' },
    { code: 'AY', name: 'Finnair' }
  ];

  for (const airline of newAirlines) {
    await testSpecificAirline(airline.code, airline.name);
  }

  // Test 4: Test fallback handling
  console.log('4. Testing fallback handling with unknown airline:');
  await testSpecificAirline('ZZ', 'Unknown Airlines');

  // Test 5: Test logo statistics
  console.log('5. Testing logo statistics:');
  const stats = airlineLogoService.getLogoStats();
  console.log(`   Cache size: ${stats.cacheSize}`);
  console.log(`   Supported airlines: ${stats.supportedAirlines}`);
  console.log('   ✅ Statistics retrieved successfully\n');

  // Test 6: Test preloading critical logos
  console.log('6. Testing critical logo preloading:');
  const preloadResults = await airlineLogoService.preloadCriticalLogos();
  const successCount = preloadResults.filter(r => r.success).length;
  console.log(`   ✅ Preloaded ${successCount}/${preloadResults.length} critical logos\n`);

  // Test 7: Generate comprehensive test PDF
  console.log('7. Generating comprehensive test PDF:');
  await generateComprehensiveTestPDF();

  console.log('🎯 Comprehensive Logo System Test Complete!');
  console.log('📋 Summary:');
  console.log(`   - Airline coverage: ${result.coverage}%`);
  console.log('   - Ethiopian Airlines: ✅ Working');
  console.log('   - New airlines added: ✅ Working');
  console.log('   - Fallback handling: ✅ Working');
  console.log('   - Logo statistics: ✅ Working');
  console.log('   - Critical preloading: ✅ Working');
  console.log('   - Test PDF generation: ✅ Complete');
}

async function runCoverageTest() {
  const pdfServiceCodes = {
    'RYANAIR': 'FR', 'EASYJET': 'U2', 'JET2': 'LS', 'VUELING': 'VY',
    'AEGEAN AIRLINES': 'A3', 'BRITISH AIRWAYS': 'BA', 'LUFTHANSA': 'LH',
    'AIR FRANCE': 'AF', 'KLM': 'KL', 'SWISS': 'LX', 'WIZZ AIR': 'W6',
    'NORWEGIAN': 'DY', 'FLYBE': 'BE', 'TWINJET': 'T7', 'IBERIA': 'IB',
    'TAP AIR PORTUGAL': 'TP', 'ALITALIA': 'AZ', 'AEROFLOT': 'SU',
    'FINNAIR': 'AY', 'CATHAY PACIFIC': 'CX', 'JAPAN AIRLINES': 'JL',
    'SINGAPORE AIRLINES': 'SQ', 'KOREAN AIR': 'KE', 'THAI AIRWAYS': 'TG',
    'MALAYSIA AIRLINES': 'MH', 'PHILIPPINE AIRLINES': 'PR',
    'CHINA AIRLINES': 'CI', 'CHINA EASTERN': 'MU', 'CHINA SOUTHERN': 'CZ',
    'ASIANA AIRLINES': 'OZ', 'VIETNAM AIRLINES': 'VN',
    'GARUDA INDONESIA': 'GA', 'CEBU PACIFIC': '5J', 'JETSTAR ASIA': '3K',
    'TURKISH AIRLINES': 'TK', 'EMIRATES': 'EK', 'QATAR AIRWAYS': 'QR',
    'ETIHAD AIRWAYS': 'EY', 'SAUDIA': 'SV', 'GULF AIR': 'GF',
    'OMAN AIR': 'WY', 'FLYDUBAI': 'FZ', 'AIR ARABIA': 'G9',
    'ETHIOPIAN AIRLINES': 'ET', 'SOUTH AFRICAN AIRWAYS': 'SA',
    'KENYA AIRWAYS': 'KQ', 'EGYPTAIR': 'MS', 'ROYAL AIR MAROC': 'AT',
    'TUNISAIR': 'TU', 'AIR ALGERIE': 'AH', 'RWANDAIR': 'WB',
    'FASTJET': 'FN', 'LATAM': 'LA', 'AVIANCA': 'AV', 'COPA AIRLINES': 'CM',
    'GOL': 'G3', 'AZUL': 'AD', 'SKY AIRLINE': 'H2', 'VIVA AIR': 'VV',
    'QANTAS': 'QF', 'JETSTAR': 'JQ', 'AIR NEW ZEALAND': 'NZ',
    'VIRGIN AUSTRALIA': 'VA', 'TIGERAIR': 'TT', 'AMERICAN AIRLINES': 'AA',
    'DELTA AIR LINES': 'DL', 'UNITED AIRLINES': 'UA'
  };

  let supported = 0;
  for (const [airlineName, airlineCode] of Object.entries(pdfServiceCodes)) {
    const logoSources = airlineLogoService.getLogoSources(airlineCode);
    if (logoSources) supported++;
  }

  return {
    total: Object.keys(pdfServiceCodes).length,
    supported,
    coverage: ((supported / Object.keys(pdfServiceCodes).length) * 100).toFixed(1)
  };
}

async function testSpecificAirline(code, name) {
  try {
    const logo = await airlineLogoService.getLogoForPDF(code, name);
    const logoType = logo.startsWith('data:image/png') ? 'PNG' : 
                    logo.startsWith('data:image/svg') ? 'SVG' : 'URL';
    console.log(`   ✅ ${name} (${code}): ${logoType} logo (${logo.length} chars)`);
  } catch (error) {
    console.log(`   ❌ ${name} (${code}): ${error.message}`);
  }
}

async function generateComprehensiveTestPDF() {
  const testTicketData = {
    tripDates: '21 JUL 2025 › 29 JUL 2025',
    destination: 'COMPREHENSIVE AIRLINE LOGO TEST',
    passengers: [
      { name: 'LOGO/TESTER' },
      { name: 'SYSTEM/VALIDATOR' }
    ],
    reservationCode: 'LOGOTEST',
    airlineReservationCode: 'COMP001',
    segments: [
      {
        airline: 'ETHIOPIAN AIRLINES',
        flightNo: 'ET 900',
        from: { code: 'ADD', city: 'Addis Ababa Bole International Airport' },
        to: { code: 'LHR', city: 'London Heathrow Airport' },
        departureTime: '15:15',
        arrivalTime: '19:05',
        departureDay: 'MONDAY, JUL 21',
        duration: '8H 50M',
        flightClass: 'Economy Class (M)',
        stops: 0
      },
      {
        airline: 'CATHAY PACIFIC',
        flightNo: 'CX 254',
        from: { code: 'LHR', city: 'London Heathrow Airport' },
        to: { code: 'HKG', city: 'Hong Kong International Airport' },
        departureTime: '21:30',
        arrivalTime: '17:45+1',
        departureDay: 'TUESDAY, JUL 29',
        duration: '11H 15M',
        flightClass: 'Economy Class (M)',
        stops: 0
      }
    ]
  };

  try {
    const outputPath = './test-comprehensive-logo-system.pdf';
    await pdfService.generatePDF(testTicketData, outputPath);
    console.log(`   ✅ Test PDF generated: ${outputPath}`);
  } catch (error) {
    console.log(`   ❌ PDF generation failed: ${error.message}`);
  }
}

// Run the comprehensive test
testComprehensiveLogoSystem().catch(console.error);
