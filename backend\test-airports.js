// Test script for the comprehensive airport service
const airportService = require('./services/airportService');

console.log('🧪 Testing Comprehensive Airport Service\n');

// Test 1: Total airport count
console.log(`📊 Total airports in database: ${airportService.getTotalAirportCount()}`);

// Test 2: Search by IATA code
console.log('\n🔍 Testing IATA code searches:');
const iataTests = ['LHR', 'JFK', 'DXB', 'NRT', 'SYD'];
iataTests.forEach(code => {
  const results = airportService.searchAirports(code, 3);
  console.log(`  ${code}: ${results.length} results - ${results[0]?.displayName || 'Not found'}`);
});

// Test 3: Search by city name
console.log('\n🏙️ Testing city name searches:');
const cityTests = ['London', 'Paris', 'Tokyo', 'New York', 'Dubai'];
cityTests.forEach(city => {
  const results = airportService.searchAirports(city, 3);
  console.log(`  ${city}: ${results.length} results`);
  results.forEach((airport, i) => {
    console.log(`    ${i + 1}. ${airport.displayName}`);
  });
});

// Test 4: Search by country
console.log('\n🌍 Testing country searches:');
const countryTests = ['United Kingdom', 'Germany', 'Japan'];
countryTests.forEach(country => {
  const results = airportService.getAirportsByCountry(country, 5);
  console.log(`  ${country}: ${results.length} airports`);
});

// Test 5: Popular airports
console.log('\n⭐ Popular airports:');
const popular = airportService.getPopularAirports(10);
popular.forEach((airport, i) => {
  console.log(`  ${i + 1}. ${airport.displayName}`);
});

// Test 6: Regional searches
console.log('\n🌏 Testing regional searches:');
const regions = ['europe', 'asia', 'north-america'];
regions.forEach(region => {
  const results = airportService.getAirportsByRegion(region, 5);
  console.log(`  ${region}: ${results.length} airports`);
});

console.log('\n✅ Airport service test completed!');
