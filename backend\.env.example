# Server Configuration
PORT=5000
NODE_ENV=development

# Flight API Configuration - Add your fresh API credentials here
# FLIGHT_API_KEY=your_flight_api_key_here
# FLIGHT_API_BASE_URL=your_flight_api_base_url

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# PayPal Configuration
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=VerifiedOnward <<EMAIL>>

# Frontend URL
FRONTEND_URL=http://localhost:5173
