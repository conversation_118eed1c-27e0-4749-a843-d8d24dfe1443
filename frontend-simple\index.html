<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VerifiedOnward - Embassy-Approved Flight Reservations</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .header {
            background: rgba(13, 27, 42, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            margin: 2rem;
            border-radius: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }
        .tagline {
            opacity: 0.8;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
        .status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        .status-online { background: #22c55e; }
        .status-offline { background: #ef4444; }
        .main-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        .hero {
            text-align: center;
            margin-bottom: 3rem;
        }
        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .hero p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        .card {
            background: white;
            color: #333;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .card h3 {
            color: #0D1B2A;
            margin-bottom: 1.5rem;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #374151;
        }
        input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.2s;
        }
        input:focus {
            outline: none;
            border-color: #6366f1;
        }
        .button-group {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
        }
        button {
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }
        .btn-primary {
            background: linear-gradient(135deg, #6366F1, #7C3AED);
            color: white;
        }
        .btn-secondary {
            background: linear-gradient(135deg, #64748b, #475569);
            color: white;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }
        button:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }
        .result {
            margin-top: 1.5rem;
            padding: 1rem;
            border-radius: 10px;
            border: 2px solid;
        }
        .result.success {
            background: #f0fdf4;
            border-color: #22c55e;
            color: #16a34a;
        }
        .result.error {
            background: #fef2f2;
            border-color: #ef4444;
            color: #dc2626;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        function App() {
            const [backendStatus, setBackendStatus] = useState('checking');
            const [bookingRef, setBookingRef] = useState('CATHAY001');
            const [passengers, setPassengers] = useState([
                { firstName: 'JANE', lastName: 'COOPER' },
                { firstName: 'JENNY', lastName: 'WILSON' }
            ]);
            const [result, setResult] = useState('');
            const [loading, setLoading] = useState(false);

            useEffect(() => {
                checkBackendStatus();
            }, []);

            const checkBackendStatus = async () => {
                try {
                    const response = await axios.get('http://localhost:5001/api/health');
                    setBackendStatus('online');
                } catch (error) {
                    setBackendStatus('offline');
                }
            };

            const generatePDF = async () => {
                setLoading(true);
                setResult('');
                
                try {
                    const requestData = {
                        bookingReference: bookingRef,
                        passengers: passengers.filter(p => p.firstName && p.lastName),
                        outboundFlight: {
                            airline: 'CATHAY PACIFIC',
                            flightNumber: 'CX 784',
                            departureDate: '2021-07-18',
                            departureTime: '16:05',
                            arrivalTime: '21:05',
                            duration: '05hr(s) 00min(s)',
                            aircraft: 'AIRBUS INDUSTRIE A330-300',
                            departureAirport: {
                                iata: 'DPS',
                                city: 'Denpasar-Bali',
                                country: 'Indonesia'
                            },
                            arrivalAirport: {
                                iata: 'HKG',
                                city: 'Hong Kong',
                                country: 'Hong Kong'
                            }
                        },
                        returnFlight: {
                            airline: 'CATHAY PACIFIC',
                            flightNumber: 'CX 844',
                            departureDate: '2021-07-19',
                            departureTime: '02:05',
                            arrivalTime: '06:00',
                            duration: '15hr(s) 55min(s)',
                            aircraft: 'BOEING 777-300ER',
                            departureAirport: {
                                iata: 'HKG',
                                city: 'Hong Kong',
                                country: 'Hong Kong'
                            },
                            arrivalAirport: {
                                iata: 'JFK',
                                city: 'New York',
                                country: 'United States Of America'
                            }
                        }
                    };

                    const response = await axios.post('http://localhost:5001/api/tickets/generate', requestData);
                    
                    if (response.data.success) {
                        setResult({ type: 'success', message: `✅ PDF Generated! Booking: ${bookingRef}` });
                    } else {
                        setResult({ type: 'error', message: `❌ Error: ${response.data.error}` });
                    }
                } catch (error) {
                    setResult({ type: 'error', message: `❌ Request failed: ${error.message}` });
                } finally {
                    setLoading(false);
                }
            };

            const downloadPDF = () => {
                if (bookingRef) {
                    window.open(`http://localhost:5001/api/tickets/download/${bookingRef}`, '_blank');
                }
            };

            return (
                <div>
                    <header className="header">
                        <div>
                            <div className="logo">VerifiedOnward</div>
                            <div className="tagline">Embassy-Approved Flight Reservations</div>
                        </div>
                        <div className="status">
                            <div className={`status-dot ${backendStatus === 'online' ? 'status-online' : 'status-offline'}`}></div>
                            <span>Backend {backendStatus === 'online' ? 'Online' : 'Offline'}</span>
                        </div>
                    </header>

                    <div className="main-container">
                        <div className="hero">
                            <h1>🎯 Embassy-Ready Flight Reservations</h1>
                            <p>Generate professional Cathay Pacific-style flight documents in 60 seconds</p>
                        </div>

                        <div className="card">
                            <h3>🎫 Generate Flight Reservation</h3>

                            <div className="form-group">
                                <label>Booking Reference:</label>
                                <input
                                    type="text"
                                    value={bookingRef}
                                    onChange={(e) => setBookingRef(e.target.value)}
                                    placeholder="e.g., CATHAY001"
                                />
                            </div>

                            {passengers.map((passenger, index) => (
                                <div key={index} className="form-row">
                                    <div className="form-group">
                                        <label>Passenger {index + 1} - First Name:</label>
                                        <input
                                            type="text"
                                            value={passenger.firstName}
                                            onChange={(e) => {
                                                const newPassengers = [...passengers];
                                                newPassengers[index].firstName = e.target.value;
                                                setPassengers(newPassengers);
                                            }}
                                            placeholder="First name"
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>Passenger {index + 1} - Last Name:</label>
                                        <input
                                            type="text"
                                            value={passenger.lastName}
                                            onChange={(e) => {
                                                const newPassengers = [...passengers];
                                                newPassengers[index].lastName = e.target.value;
                                                setPassengers(newPassengers);
                                            }}
                                            placeholder="Last name"
                                        />
                                    </div>
                                </div>
                            ))}

                            <div className="button-group">
                                <button
                                    className="btn-primary"
                                    onClick={generatePDF}
                                    disabled={loading}
                                >
                                    {loading ? '🔄 Generating...' : '🎯 Generate Cathay Pacific PDF'}
                                </button>
                                
                                <button
                                    className="btn-secondary"
                                    onClick={downloadPDF}
                                >
                                    📥 Download PDF
                                </button>
                            </div>

                            {result && (
                                <div className={`result ${result.type}`}>
                                    {result.message}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
