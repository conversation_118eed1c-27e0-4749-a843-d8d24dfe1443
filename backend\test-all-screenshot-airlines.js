const pdfService = require('./services/pdfService');
const airlineLogoService = require('./services/airlineLogoService');

/**
 * Comprehensive test for ALL airlines from the provided screenshots
 * Tests: JET2, KLM, VUELING, RYANAIR UK
 */
async function testAllScreenshotAirlines() {
  console.log('🎯 COMPREHENSIVE TEST: All Airlines from Screenshots\n');

  // Test data with all 4 airlines from screenshots
  const testData = {
    tripDates: '21 JUL 2025 › 28 JUL 2025',
    destination: 'COMPREHENSIVE AIRLINE LOGO VERIFICATION TEST',
    passengers: [
      { name: 'SCRE<PERSON><PERSON><PERSON><PERSON>', surname: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
      { name: '<PERSON><PERSON><PERSON><PERSON>', surname: 'TES<PERSON><PERSON>' }
    ],
    reservationCode: 'SCREEN01',
    airlineReservationCode: 'LOGO01',
    segments: [
      {
        airline: 'JET2',
        flightNo: 'LS 803',
        route: 'MAN – BCN',
        duration: '2H 35M',
        stops: 0,
        departure: {
          date: 'MONDAY, JUL 21',
          airport: 'MAN',
          airportName: 'Manchester Airport',
          time: '08:00',
          terminal: 'TERMINAL 1'
        },
        arrival: {
          airport: 'BCN',
          airportName: 'Josep Tarradellas Barcelona-El Prat Airport',
          time: '11:35',
          terminal: 'TERMINAL 1'
        },
        aircraft: 'BOEING 737-800',
        flightClass: 'Economy Class (M)',
        status: 'CONFIRMED'
      },
      {
        airline: 'KLM',
        flightNo: 'KL 1518',
        route: 'BCN – AMS',
        duration: '2H 15M',
        stops: 0,
        departure: {
          date: 'MONDAY, JUL 21',
          airport: 'BCN',
          airportName: 'Josep Tarradellas Barcelona-El Prat Airport',
          time: '14:20',
          terminal: 'TERMINAL 1'
        },
        arrival: {
          airport: 'AMS',
          airportName: 'Amsterdam Airport Schiphol',
          time: '17:35',
          terminal: 'TERMINAL 1'
        },
        aircraft: 'BOEING 737-800',
        flightClass: 'Economy Class (M)',
        status: 'CONFIRMED'
      },
      {
        airline: 'VUELING',
        flightNo: 'VY 8747',
        route: 'AMS – MAN',
        duration: '1H 25M',
        stops: 0,
        departure: {
          date: 'MONDAY, JUL 28',
          airport: 'AMS',
          airportName: 'Amsterdam Airport Schiphol',
          time: '19:20',
          terminal: 'TERMINAL 1'
        },
        arrival: {
          airport: 'MAN',
          airportName: 'Manchester Airport',
          time: '19:45',
          terminal: 'TERMINAL 1'
        },
        aircraft: 'AIRBUS A319',
        flightClass: 'Economy Class (M)',
        status: 'CONFIRMED'
      },
      {
        airline: 'RYANAIR UK',
        flightNo: 'RK 5270',
        route: 'MAN – BCN',
        duration: '2H 35M',
        stops: 0,
        departure: {
          date: 'MONDAY, JUL 28',
          airport: 'MAN',
          airportName: 'Manchester Airport',
          time: '22:10',
          terminal: 'TERMINAL 1'
        },
        arrival: {
          airport: 'BCN',
          airportName: 'Josep Tarradellas Barcelona-El Prat Airport',
          time: '01:45',
          terminal: 'TERMINAL 1'
        },
        aircraft: 'BOEING 737-800',
        flightClass: 'Economy Class (M)',
        status: 'CONFIRMED'
      }
    ],
    showNotice: true,
    customNotice: "COMPREHENSIVE TEST: Verifying all airlines from screenshots display authentic brand logos."
  };

  console.log('📋 Airlines being tested:');
  testData.segments.forEach((segment, index) => {
    console.log(`   ${index + 1}. ${segment.airline} ${segment.flightNo} (${segment.route})`);
  });

  console.log('\n🏷️ Airline Code Mapping Results:');
  const mappingResults = [];
  for (const segment of testData.segments) {
    const airlineCode = pdfService.getAirlineCode(segment.airline);
    mappingResults.push({ airline: segment.airline, code: airlineCode });
    console.log(`   ${segment.airline} → ${airlineCode}`);
  }

  console.log('\n🎨 Logo Retrieval Results:');
  const logoResults = [];
  for (const segment of testData.segments) {
    const airlineCode = pdfService.getAirlineCode(segment.airline);
    try {
      const logo = await airlineLogoService.getLogoForPDF(airlineCode, segment.airline);
      const isBase64PNG = logo.startsWith('data:image/png;base64,');
      const isSVG = logo.startsWith('data:image/svg');
      
      const result = {
        airline: segment.airline,
        code: airlineCode,
        type: isBase64PNG ? 'Authentic PNG' : isSVG ? 'Generated SVG' : 'Other',
        size: logo.length,
        success: isBase64PNG
      };
      
      logoResults.push(result);
      
      const status = isBase64PNG ? '✅' : isSVG ? '⚠️' : '❌';
      console.log(`   ${status} ${segment.airline} (${airlineCode}): ${result.type} (${result.size} chars)`);
    } catch (error) {
      logoResults.push({
        airline: segment.airline,
        code: airlineCode,
        type: 'ERROR',
        size: 0,
        success: false,
        error: error.message
      });
      console.log(`   ❌ ${segment.airline} (${airlineCode}): ERROR - ${error.message}`);
    }
  }

  console.log('\n📄 Generating comprehensive test PDF...');
  try {
    const outputPath = './test-outputs/all-screenshot-airlines-test.pdf';
    await pdfService.generatePDF(testData, outputPath);
    console.log(`   ✅ PDF generated: ${outputPath}`);

    // Summary
    console.log('\n📊 RESULTS SUMMARY:');
    const successCount = logoResults.filter(r => r.success).length;
    const totalCount = logoResults.length;
    
    console.log(`   Success Rate: ${successCount}/${totalCount} (${Math.round(successCount/totalCount*100)}%)`);
    
    console.log('\n   Detailed Results:');
    logoResults.forEach(result => {
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      console.log(`     ${status} ${result.airline} → ${result.code} → ${result.type}`);
    });

    console.log('\n🎯 VISUAL VERIFICATION REQUIRED:');
    console.log('   Please open the generated PDF and verify:');
    console.log('   □ JET2 shows recognizable orange Jet2 brand logo');
    console.log('   □ KLM shows recognizable blue KLM crown logo');
    console.log('   □ VUELING shows recognizable yellow/red Vueling logo');
    console.log('   □ RYANAIR UK shows recognizable blue/yellow Ryanair logo');
    console.log('   □ All logos are clear, properly sized, and professional');
    console.log('   □ No text placeholders like "JET2 LS 803" appear instead of logos');
    console.log('   □ No broken image icons or generic symbols');

    return { outputPath, results: logoResults, successRate: successCount/totalCount };

  } catch (error) {
    console.error(`   ❌ PDF generation failed: ${error.message}`);
    throw error;
  }
}

// Run the comprehensive test
testAllScreenshotAirlines()
  .then(({ outputPath, results, successRate }) => {
    console.log(`\n🎉 COMPREHENSIVE TEST COMPLETE!`);
    console.log(`📁 Generated PDF: ${outputPath}`);
    console.log(`📊 Success Rate: ${Math.round(successRate * 100)}%`);
    
    if (successRate === 1.0) {
      console.log('🎯 ALL AIRLINES PASSED - Authentic logos should be visible in PDF!');
    } else {
      console.log('⚠️ Some airlines may still show placeholder logos - manual verification needed.');
    }
  })
  .catch(error => {
    console.error('\n❌ Comprehensive test failed:', error);
    process.exit(1);
  });
