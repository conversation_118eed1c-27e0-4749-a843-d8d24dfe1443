const puppeteer = require('puppeteer');

async function testWhiteScreenIssue() {
  console.log('🚀 Starting white screen issue test...');
  
  const browser = await puppeteer.launch({ 
    headless: false, // Show browser for debugging
    devtools: true   // Open devtools
  });
  
  try {
    const page = await browser.newPage();
    
    // Listen for console messages
    page.on('console', msg => {
      console.log(`🔍 BROWSER: ${msg.type()}: ${msg.text()}`);
    });
    
    // Listen for errors
    page.on('error', err => {
      console.error('❌ PAGE ERROR:', err);
    });
    
    page.on('pageerror', err => {
      console.error('❌ PAGE ERROR:', err);
    });
    
    // Navigate to payment debug page
    console.log('📍 Navigating to payment debug page...');
    await page.goto('http://localhost:5173/payment-debug', { waitUntil: 'networkidle2' });
    
    // Wait for page to load
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if page loaded properly
    const title = await page.title();
    console.log('📄 Page title:', title);
    
    // Try to find the Show Payment button
    console.log('🔍 Looking for Show Payment button...');
    const showPaymentButton = await page.$('button:contains("Show Payment")');
    if (!showPaymentButton) {
      // Try alternative selector
      const buttons = await page.$$('button');
      for (let button of buttons) {
        const text = await page.evaluate(el => el.textContent, button);
        if (text.includes('Show Payment')) {
          console.log('✅ Found Show Payment button');

          // Click the button
          console.log('🚀 Clicking Show Payment button...');
          await button.click();

          // Wait a bit to see what happens
          await new Promise(resolve => setTimeout(resolve, 2000));

          // Check if payment section appeared
          const paymentSection = await page.$('[class*="payment"]');
          if (paymentSection) {
            console.log('✅ Payment section appeared');
          } else {
            console.log('❌ Payment section did not appear');
          }

          break;
        }
      }
    }

    // Try to find any passenger details form (fallback)
    console.log('🔍 Looking for passenger details form...');
    const passengerForm = await page.$('input[type="email"]');
    if (passengerForm) {
      console.log('✅ Found passenger form (email input)');
      
      // Fill in test data
      console.log('📝 Filling in test data...');
      await page.type('input[type="email"]', '<EMAIL>');
      
      // Look for first name input
      const firstNameInput = await page.$('input[placeholder*="First"], input[placeholder*="first"]');
      if (firstNameInput) {
        await page.type('input[placeholder*="First"], input[placeholder*="first"]', 'John');
      }
      
      // Look for last name input
      const lastNameInput = await page.$('input[placeholder*="Last"], input[placeholder*="last"]');
      if (lastNameInput) {
        await page.type('input[placeholder*="Last"], input[placeholder*="last"]', 'Doe');
      }
      
      // Look for Continue to Payment button
      console.log('🔍 Looking for Continue to Payment button...');
      const continueButton = await page.$('button[type="submit"]');
      if (continueButton) {
        const buttonText = await page.evaluate(el => el.textContent, continueButton);
        console.log('🔘 Found button:', buttonText);
        
        if (buttonText.includes('Continue to Payment')) {
          console.log('🚀 Clicking Continue to Payment button...');
          
          // Click the button and wait for response
          await continueButton.click();
          
          // Wait a bit to see what happens
          await new Promise(resolve => setTimeout(resolve, 3000));
          
          // Check if page is white/blank
          const bodyContent = await page.evaluate(() => document.body.innerHTML);
          if (bodyContent.trim() === '' || bodyContent.length < 100) {
            console.log('❌ WHITE SCREEN DETECTED!');
            console.log('Body content length:', bodyContent.length);
            console.log('Body content:', bodyContent.substring(0, 200));
          } else {
            console.log('✅ Page has content after button click');
            console.log('Body content length:', bodyContent.length);
          }
          
          // Check current URL
          const currentUrl = page.url();
          console.log('📍 Current URL:', currentUrl);
          
        } else {
          console.log('❌ Button text does not match "Continue to Payment"');
        }
      } else {
        console.log('❌ No submit button found');
      }
      
    } else {
      console.log('❌ No passenger form found');
    }
    
    // Keep browser open for manual inspection
    console.log('🔍 Keeping browser open for manual inspection...');
    console.log('Press Ctrl+C to close');
    
    // Wait indefinitely
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Test error:', error);
  } finally {
    // Don't close browser automatically for debugging
    // await browser.close();
  }
}

// Check if puppeteer is available
try {
  testWhiteScreenIssue();
} catch (error) {
  console.log('❌ Puppeteer not available. Install with: npm install puppeteer');
  console.log('Error:', error.message);
}
