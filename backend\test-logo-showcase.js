const ticketService = require('./services/ticketService');

// Comprehensive airline logo showcase test
async function createLogoShowcase() {
  console.log('🎨 Creating Airline Logo Showcase PDF...\n');

  // Create a mega flight with multiple airlines to showcase all logos
  const showcaseData = {
    flightData: {
      trips: [
        {
          type: 'outbound',
          legs: [
            {
              legNumber: 1,
              airline: { name: 'Lufthansa', code: 'LH' },
              flightNumber: 'LH441',
              departure: {
                airport: 'FRA',
                city: 'Frankfurt',
                country: 'Germany',
                datetime: '2025-07-28T08:00:00Z',
                terminal: '1'
              },
              arrival: {
                airport: 'LHR',
                city: 'London Heathrow',
                country: 'United Kingdom',
                datetime: '2025-07-28T09:30:00Z',
                terminal: '2'
              },
              duration: '1h 30m',
              stopCount: 0,
              meals: 'Available at check-in',
              baggage: 'Available at check-in',
              aircraft: 'Airbus A320',
              checkIn: '2 hr(s) before departure'
            },
            {
              legNumber: 2,
              airline: { name: 'British Airways', code: 'BA' },
              flightNumber: 'BA117',
              departure: {
                airport: 'LHR',
                city: 'London Heathrow',
                country: 'United Kingdom',
                datetime: '2025-07-28T11:15:00Z',
                terminal: '5'
              },
              arrival: {
                airport: 'JFK',
                city: 'New York',
                country: 'USA',
                datetime: '2025-07-28T14:30:00Z',
                terminal: '7'
              },
              duration: '8h 15m',
              stopCount: 0,
              meals: 'Available at check-in',
              baggage: 'Available at check-in',
              aircraft: 'Boeing 777-300ER',
              checkIn: '3 hr(s) before departure'
            },
            {
              legNumber: 3,
              airline: { name: 'Emirates', code: 'EK' },
              flightNumber: 'EK001',
              departure: {
                airport: 'JFK',
                city: 'New York',
                country: 'USA',
                datetime: '2025-07-28T18:45:00Z',
                terminal: '4'
              },
              arrival: {
                airport: 'DXB',
                city: 'Dubai',
                country: 'UAE',
                datetime: '2025-07-29T14:30:00Z',
                terminal: '3'
              },
              duration: '12h 45m',
              stopCount: 0,
              meals: 'Available at check-in',
              baggage: 'Available at check-in',
              aircraft: 'Airbus A380',
              checkIn: '3 hr(s) before departure'
            },
            {
              legNumber: 4,
              airline: { name: 'Thai Airways', code: 'TG' },
              flightNumber: 'TG635',
              departure: {
                airport: 'DXB',
                city: 'Dubai',
                country: 'UAE',
                datetime: '2025-07-29T16:15:00Z',
                terminal: '3'
              },
              arrival: {
                airport: 'BKK',
                city: 'Bangkok',
                country: 'Thailand',
                datetime: '2025-07-30T01:20:00Z',
                terminal: '1'
              },
              duration: '6h 05m',
              stopCount: 0,
              meals: 'Available at check-in',
              baggage: 'Available at check-in',
              aircraft: 'Boeing 787-9',
              checkIn: '2 hr(s) before departure'
            }
          ]
        },
        {
          type: 'return',
          legs: [
            {
              legNumber: 1,
              airline: { name: 'SAS', code: 'SK' },
              flightNumber: 'SK4610',
              departure: {
                airport: 'BKK',
                city: 'Bangkok',
                country: 'Thailand',
                datetime: '2025-08-05T10:30:00Z',
                terminal: '1'
              },
              arrival: {
                airport: 'OSL',
                city: 'Oslo',
                country: 'Norway',
                datetime: '2025-08-05T15:45:00Z',
                terminal: 'Not assigned'
              },
              duration: '11h 15m',
              stopCount: 0,
              meals: 'Available at check-in',
              baggage: 'Available at check-in',
              aircraft: 'Boeing 787-9',
              checkIn: '3 hr(s) before departure'
            },
            {
              legNumber: 2,
              airline: { name: 'Air France', code: 'AF' },
              flightNumber: 'AF1234',
              departure: {
                airport: 'OSL',
                city: 'Oslo',
                country: 'Norway',
                datetime: '2025-08-05T18:20:00Z',
                terminal: 'Not assigned'
              },
              arrival: {
                airport: 'CDG',
                city: 'Paris Charles de Gaulle',
                country: 'France',
                datetime: '2025-08-05T20:45:00Z',
                terminal: '2F'
              },
              duration: '2h 25m',
              stopCount: 0,
              meals: 'Available at check-in',
              baggage: 'Available at check-in',
              aircraft: 'Airbus A320',
              checkIn: '2 hr(s) before departure'
            },
            {
              legNumber: 3,
              airline: { name: 'KLM', code: 'KL' },
              flightNumber: 'KL1006',
              departure: {
                airport: 'CDG',
                city: 'Paris Charles de Gaulle',
                country: 'France',
                datetime: '2025-08-05T22:30:00Z',
                terminal: '2E'
              },
              arrival: {
                airport: 'AMS',
                city: 'Amsterdam Schiphol',
                country: 'Netherlands',
                datetime: '2025-08-05T23:45:00Z',
                terminal: 'Not assigned'
              },
              duration: '1h 15m',
              stopCount: 0,
              meals: 'Available at check-in',
              baggage: 'Available at check-in',
              aircraft: 'Boeing 737-800',
              checkIn: '2 hr(s) before departure'
            }
          ]
        }
      ]
    },
    passengerData: [
      { firstName: 'AIRLINE', lastName: 'SHOWCASE' }
    ],
    bookingReference: 'LOGOS1',
    email: '<EMAIL>'
  };

  try {
    console.log('📄 Generating comprehensive airline logo showcase PDF...');
    console.log('   Airlines included: Lufthansa, British Airways, Emirates, Thai Airways, SAS, Air France, KLM');
    console.log('   Total legs: 7 (4 outbound + 3 return)');
    
    const result = await ticketService.generateTicketPDF(showcaseData);
    
    if (result && result.pdfBuffer) {
      console.log('\n✅ Airline Logo Showcase PDF generated successfully!');
      console.log(`📊 File size: ${result.pdfBuffer.length} bytes`);
      console.log(`💾 Saved to: ${result.pdfPath}`);
      console.log('\n🎨 This PDF showcases logos from 7 major airlines:');
      console.log('   • Lufthansa (LH) - German flag carrier');
      console.log('   • British Airways (BA) - UK flag carrier');
      console.log('   • Emirates (EK) - UAE premium airline');
      console.log('   • Thai Airways (TG) - Thailand flag carrier');
      console.log('   • SAS (SK) - Scandinavian Airlines');
      console.log('   • Air France (AF) - French flag carrier');
      console.log('   • KLM (KL) - Dutch flag carrier');
      
      console.log('\n📋 Logo Sources:');
      console.log('   • Primary: Google Static Flight Logos (most reliable)');
      console.log('   • Fallback: Generated SVG with airline branding');
      console.log('   • Cache: In-memory caching for performance');
      
      return result.pdfPath;
    } else {
      console.log('❌ Showcase PDF generation failed');
      return null;
    }
  } catch (error) {
    console.log('❌ Showcase PDF generation error:', error.message);
    return null;
  }
}

// Run showcase if this file is executed directly
if (require.main === module) {
  createLogoShowcase().then(pdfPath => {
    if (pdfPath) {
      console.log('\n🚀 Opening showcase PDF...');
      require('child_process').exec(`open "${pdfPath}"`);
    }
  }).catch(console.error);
}

module.exports = { createLogoShowcase };
