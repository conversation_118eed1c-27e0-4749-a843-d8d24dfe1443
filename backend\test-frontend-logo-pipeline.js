const pdfService = require('./services/pdfService');
const airlineLogoService = require('./services/airlineLogoService');

/**
 * Test the complete airline logo pipeline as it would work from frontend requests
 * This simulates the exact data flow from frontend → backend → PDF generation
 */
async function testFrontendLogoPipeline() {
  console.log('🔍 Testing Frontend → Backend → PDF Logo Pipeline\n');

  // Simulate frontend request data for airlines from screenshots
  const frontendRequestData = {
    bookingReference: "LOGO-TEST",
    passengers: [
      { id: 1, firstName: "LOGO", lastName: "VERIFICATION" },
      { id: 2, firstName: "AIRLINE", lastName: "TESTING" }
    ],
    outboundFlight: {
      airline: "Jet2",           // Frontend sends "Jet2"
      flightNumber: "LS 803",
      duration: "2h 35m",
      aircraft: "Boeing 737-800",
      stops: 0,
      layovers: null,
      departure: {
        code: "MAN",
        city: "Manchester Airport",
        time: "2025-07-21 08:00",
        terminal: "1"
      },
      arrival: {
        code: "BCN",
        city: "Josep Tarradellas Barcelona-El Prat Airport",
        time: "2025-07-21 11:35",
        terminal: "1"
      }
    },
    returnFlight: {
      airline: "Vueling",        // Frontend sends "Vueling"
      flightNumber: "VY 8747",
      duration: "2h 25m",
      aircraft: "Airbus A319",
      stops: 0,
      layovers: null,
      departure: {
        code: "BCN",
        city: "Josep Tarradellas Barcelona-El Prat Airport",
        time: "2025-07-28 12:45",
        terminal: "1"
      },
      arrival: {
        code: "MAN",
        city: "Manchester Airport",
        time: "2025-07-28 15:10",
        terminal: "1"
      }
    },
    totalPrice: 4.99
  };

  console.log('1. 📥 Frontend Request Data:');
  console.log(`   Outbound: ${frontendRequestData.outboundFlight.airline} ${frontendRequestData.outboundFlight.flightNumber}`);
  console.log(`   Return: ${frontendRequestData.returnFlight.airline} ${frontendRequestData.returnFlight.flightNumber}`);

  // Step 2: Transform to PDF service format (as done in routes/tickets.js)
  const pdfData = {
    tripDates: '21 JUL 2025 › 28 JUL 2025',
    destination: 'TRIP TO JOSEP TARRADELLAS BARCELONA-EL PRAT AIRPORT',
    passengers: frontendRequestData.passengers.map(p => ({
      name: p.firstName,
      surname: p.lastName
    })),
    reservationCode: frontendRequestData.bookingReference,
    airlineReservationCode: 'TEST01',
    segments: [
      {
        airline: frontendRequestData.outboundFlight.airline.toUpperCase(),  // "JET2"
        flightNo: frontendRequestData.outboundFlight.flightNumber,
        route: `${frontendRequestData.outboundFlight.departure.code} – ${frontendRequestData.outboundFlight.arrival.code}`,
        duration: frontendRequestData.outboundFlight.duration.toUpperCase(),
        stops: frontendRequestData.outboundFlight.stops,
        departure: {
          date: 'MONDAY, JUL 21',
          airport: frontendRequestData.outboundFlight.departure.code,
          airportName: frontendRequestData.outboundFlight.departure.city,
          time: '08:00',
          terminal: `TERMINAL ${frontendRequestData.outboundFlight.departure.terminal}`
        },
        arrival: {
          airport: frontendRequestData.outboundFlight.arrival.code,
          airportName: frontendRequestData.outboundFlight.arrival.city,
          time: '11:35',
          terminal: `TERMINAL ${frontendRequestData.outboundFlight.arrival.terminal}`
        },
        aircraft: frontendRequestData.outboundFlight.aircraft.toUpperCase(),
        flightClass: 'Economy Class (M)',
        status: 'CONFIRMED'
      },
      {
        airline: frontendRequestData.returnFlight.airline.toUpperCase(),   // "VUELING"
        flightNo: frontendRequestData.returnFlight.flightNumber,
        route: `${frontendRequestData.returnFlight.departure.code} – ${frontendRequestData.returnFlight.arrival.code}`,
        duration: frontendRequestData.returnFlight.duration.toUpperCase(),
        stops: frontendRequestData.returnFlight.stops,
        departure: {
          date: 'MONDAY, JUL 28',
          airport: frontendRequestData.returnFlight.departure.code,
          airportName: frontendRequestData.returnFlight.departure.city,
          time: '12:45',
          terminal: `TERMINAL ${frontendRequestData.returnFlight.departure.terminal}`
        },
        arrival: {
          airport: frontendRequestData.returnFlight.arrival.code,
          airportName: frontendRequestData.returnFlight.arrival.city,
          time: '15:10',
          terminal: `TERMINAL ${frontendRequestData.returnFlight.arrival.terminal}`
        },
        aircraft: frontendRequestData.returnFlight.aircraft.toUpperCase(),
        flightClass: 'Economy Class (M)',
        status: 'CONFIRMED'
      }
    ],
    showNotice: true,
    customNotice: "FRONTEND LOGO PIPELINE TEST - Verifying authentic airline logos display correctly."
  };

  console.log('\n2. 🔄 PDF Service Data Transformation:');
  for (const segment of pdfData.segments) {
    console.log(`   ${segment.airline} ${segment.flightNo} (${segment.route})`);
  }

  // Step 3: Test airline code mapping
  console.log('\n3. 🏷️ Airline Code Mapping:');
  for (const segment of pdfData.segments) {
    const airlineCode = pdfService.getAirlineCode(segment.airline);
    console.log(`   ${segment.airline} → ${airlineCode}`);
  }

  // Step 4: Test logo retrieval
  console.log('\n4. 🎨 Logo Retrieval:');
  for (const segment of pdfData.segments) {
    const airlineCode = pdfService.getAirlineCode(segment.airline);
    try {
      const logo = await airlineLogoService.getLogoForPDF(airlineCode, segment.airline);
      const isBase64PNG = logo.startsWith('data:image/png;base64,');
      const isSVG = logo.startsWith('data:image/svg');
      const isURL = logo.startsWith('http');
      
      console.log(`   ${segment.airline} (${airlineCode}):`);
      console.log(`     Type: ${isBase64PNG ? '✅ Authentic PNG' : isSVG ? '⚠️ Generated SVG' : isURL ? '🔗 External URL' : '❌ Unknown'}`);
      console.log(`     Size: ${logo.length} chars`);
      console.log(`     Preview: ${logo.substring(0, 50)}...`);
    } catch (error) {
      console.log(`   ${segment.airline} (${airlineCode}): ❌ ERROR - ${error.message}`);
    }
  }

  // Step 5: Generate actual PDF
  console.log('\n5. 📄 PDF Generation:');
  try {
    const outputPath = './test-outputs/frontend-logo-pipeline-test.pdf';
    await pdfService.generatePDF(pdfData, outputPath);
    console.log(`   ✅ PDF generated: ${outputPath}`);
    
    // Open the PDF for visual inspection
    console.log('\n6. 👁️ Visual Inspection:');
    console.log('   Opening PDF for manual verification...');
    
    return outputPath;
  } catch (error) {
    console.error(`   ❌ PDF generation failed: ${error.message}`);
    throw error;
  }
}

// Run the test
testFrontendLogoPipeline()
  .then(pdfPath => {
    console.log('\n🎯 Test Complete!');
    console.log('\n📋 Manual Verification Checklist:');
    console.log('   □ JET2 shows orange Jet2 brand logo (not "JET2 LS 803" text)');
    console.log('   □ VUELING shows yellow/red Vueling brand logo (not "VUELING VY 8747" text)');
    console.log('   □ Logos are clear, properly sized, and recognizable');
    console.log('   □ No broken image icons or placeholder symbols');
    console.log('   □ Logos match professional airline branding standards');
    console.log(`\n📁 Generated PDF: ${pdfPath}`);
  })
  .catch(error => {
    console.error('\n❌ Pipeline test failed:', error);
    process.exit(1);
  });
