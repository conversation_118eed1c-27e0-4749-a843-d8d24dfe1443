<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f8ff;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Page</h1>
        <p>This is a simple HTML test page.</p>
        <p>If you can see this, the web server is working.</p>
        <p>Current URL: <span id="url"></span></p>
        <p>Current Time: <span id="time"></span></p>
        <button onclick="testJS()">Test JavaScript</button>
        <div id="js-result"></div>
    </div>

    <script>
        document.getElementById('url').textContent = window.location.href;
        document.getElementById('time').textContent = new Date().toISOString();
        
        function testJS() {
            document.getElementById('js-result').innerHTML = '<p style="color: green;">✅ JavaScript is working!</p>';
            console.log('✅ JavaScript test successful');
        }
        
        console.log('🧪 Test page loaded successfully');
    </script>
</body>
</html>
