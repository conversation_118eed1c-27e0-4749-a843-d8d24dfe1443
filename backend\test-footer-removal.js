const pdfService = require('./services/pdfService');
const path = require('path');
const fs = require('fs').promises;

/**
 * Test footer removal across multiple airlines
 * Verifies that VerifiedOnward.com footer is completely absent from all generated PDFs
 */
async function testFooterRemoval() {
  console.log('🧹 Testing Footer Removal Across Multiple Airlines\n');

  const testCases = [
    {
      name: 'Cathay Pacific',
      airline: 'CATHAY PACIFIC',
      flightNo: 'CX 841',
      from: { code: 'HKG', city: 'Hong Kong International', time: '23:45', terminal: '1' },
      to: { code: 'JFK', city: 'New York JFK', time: '05:30', terminal: '4' },
      filename: 'cathay-pacific-footer-test.pdf'
    },
    {
      name: 'EasyJet',
      airline: 'EASYJET',
      flightNo: 'U2 2273',
      from: { code: 'MAN', city: 'Manchester Airport', time: '05:55', terminal: '1' },
      to: { code: 'MLA', city: 'Malta International Airport', time: '10:25', terminal: '1' },
      filename: 'easyjet-footer-test.pdf'
    },
    {
      name: 'British Airways',
      airline: 'BRITISH AIRWAYS',
      flightNo: 'BA 177',
      from: { code: 'LHR', city: 'London Heathrow', time: '22:30', terminal: '5' },
      to: { code: 'DXB', city: 'Dubai International', time: '08:45', terminal: '3' },
      filename: 'british-airways-footer-test.pdf'
    }
  ];

  const results = [];
  const outputDir = path.join(__dirname, 'test-outputs');
  await fs.mkdir(outputDir, { recursive: true });

  for (const testCase of testCases) {
    console.log(`🧪 Testing ${testCase.name}...`);
    
    const ticketData = {
      tripDates: '28 JUL 2025 • 04 AUG 2025',
      destination: 'TRIP TO MALTA INTERNATIONAL AIRPORT',
      passengers: [
        { name: 'MISRATI/HUDIFA MR.' },
        { name: 'ZEMIT/ATEDAL MR.' }
      ],
      reservationCode: `IDT${Date.now()}VV9X`,
      airlineReservationCode: `IDT${Date.now()}VV9X`,
      segments: [
        {
          airline: testCase.airline,
          flightNo: testCase.flightNo,
          from: testCase.from,
          to: testCase.to,
          departureDay: 'MONDAY, JUL 28',
          duration: '3h 30m',
          flightClass: 'Economy Class (M)',
          aircraft: 'Boeing 737-800',
          distance: 'Not Available',
          stops: '0',
          meals: 'Not Available'
        }
      ],
      showNotice: false
    };

    try {
      const outputPath = path.join(outputDir, testCase.filename);
      const pdfBuffer = await pdfService.generatePDF(ticketData, outputPath);
      
      // Read the generated HTML to verify footer is absent
      const htmlContent = await pdfService.generateTicketHTML(ticketData);
      const hasFooter = htmlContent.includes('VerifiedOnward.com') || 
                       htmlContent.includes('Professional Flight Reservations') ||
                       htmlContent.includes('Embassy-Approved') ||
                       htmlContent.includes('24/7 Support');

      results.push({
        airline: testCase.name,
        filename: testCase.filename,
        success: !hasFooter,
        fileSize: `${(pdfBuffer.length / 1024).toFixed(1)} KB`,
        footerFound: hasFooter
      });

      console.log(`  ✅ Generated: ${testCase.filename}`);
      console.log(`  📊 Size: ${(pdfBuffer.length / 1024).toFixed(1)} KB`);
      console.log(`  🧹 Footer Removed: ${hasFooter ? '❌ FAILED' : '✅ SUCCESS'}`);
      
    } catch (error) {
      console.error(`  ❌ Error generating ${testCase.name} PDF:`, error.message);
      results.push({
        airline: testCase.name,
        filename: testCase.filename,
        success: false,
        error: error.message
      });
    }
    
    console.log('');
  }

  // Summary report
  console.log('📋 FOOTER REMOVAL TEST RESULTS:');
  console.log('═'.repeat(50));
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  results.forEach(result => {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.airline.padEnd(20)} ${result.filename}`);
    if (result.footerFound) {
      console.log(`     ⚠️  Footer text still present in HTML!`);
    }
    if (result.error) {
      console.log(`     ❌ Error: ${result.error}`);
    }
  });
  
  console.log('═'.repeat(50));
  console.log(`📊 Overall Result: ${successCount}/${totalCount} tests passed`);
  
  if (successCount === totalCount) {
    console.log('🎉 SUCCESS: Footer completely removed from all airline PDFs!');
    console.log('✨ All generated PDFs now have clean, authentic airline appearance');
  } else {
    console.log('❌ FAILURE: Footer removal incomplete');
    console.log('🔧 Manual verification required for failed tests');
  }

  return {
    success: successCount === totalCount,
    results: results,
    passedTests: successCount,
    totalTests: totalCount
  };
}

// Run the test
if (require.main === module) {
  testFooterRemoval()
    .then(result => {
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testFooterRemoval };
