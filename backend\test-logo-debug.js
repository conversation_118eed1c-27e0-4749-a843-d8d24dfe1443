const airlineLogoService = require('./services/airlineLogoService');

/**
 * Debug airline logo fetching to identify issues
 */
async function debugAirlineLogos() {
  console.log('🔍 Debugging Airline Logo Integration...\n');

  const testAirlines = [
    { name: 'CATHAY PACIFIC', expectedCode: 'CX' },
    { name: 'EASYJET', expectedCode: 'U2' },
    { name: 'BRITISH AIRWAYS', expectedCode: 'BA' }
  ];

  for (const airline of testAirlines) {
    console.log(`\n📋 Testing: ${airline.name}`);
    
    try {
      // Test 1: Check if airline code mapping works
      const airlineCode = airlineLogoService.getAirlineCode ? 
        airlineLogoService.getAirlineCode(airline.name) : 
        'Unknown method';
      console.log(`  🏷️  Airline Code: ${airlineCode} (expected: ${airline.expectedCode})`);
      
      // Test 2: Check logo sources
      const logoSources = airlineLogoService.getAllLogoSources(airline.expectedCode);
      console.log(`  🌐 Logo Sources (${logoSources.length}):`);
      logoSources.slice(0, 2).forEach((source, index) => {
        console.log(`    ${index + 1}. ${source}`);
      });
      
      // Test 3: Try to fetch logo for PDF
      console.log(`  📥 Fetching logo for PDF...`);
      const logoResult = await airlineLogoService.getLogoForPDF(airline.expectedCode, airline.name);
      
      if (logoResult) {
        const isDataUrl = logoResult.startsWith('data:');
        const isHttpUrl = logoResult.startsWith('http');
        const isSvg = logoResult.includes('svg');
        const isPng = logoResult.includes('png');
        
        console.log(`  ✅ Logo Result: ${isDataUrl ? 'Data URL' : isHttpUrl ? 'HTTP URL' : 'Unknown format'}`);
        console.log(`  📄 Format: ${isSvg ? 'SVG' : isPng ? 'PNG' : 'Unknown'}`);
        console.log(`  📏 Length: ${logoResult.length} characters`);
        
        if (isDataUrl && logoResult.length > 100) {
          console.log(`  🎯 Status: SUCCESS - Logo ready for PDF`);
        } else {
          console.log(`  ⚠️  Status: POTENTIAL ISSUE - Logo may not display correctly`);
        }
      } else {
        console.log(`  ❌ Logo Result: NULL - No logo returned`);
      }
      
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
    }
  }

  console.log('\n🔧 Debugging Summary:');
  console.log('1. Check if airline codes are correctly mapped');
  console.log('2. Verify logo sources are accessible');
  console.log('3. Confirm logos are converted to data URLs for PDF embedding');
  console.log('4. Test with actual PDF generation');
}

// Run the debug test
if (require.main === module) {
  debugAirlineLogos().catch(console.error);
}

module.exports = { debugAirlineLogos };
