const airlineLogoService = require('./services/airlineLogoService');
const pdfService = require('./services/pdfService');
const path = require('path');

// Test airline logo functionality
async function testAirlineLogos() {
  console.log('🎨 Testing Airline Logo Service...\n');

  // Test 1: Individual logo fetching
  console.log('📋 Testing individual airline logo fetching:');
  const testAirlines = [
    { code: 'CX', name: 'CATHAY PACIFIC' },
    { code: 'BA', name: 'BRITISH AIRWAYS' },
    { code: 'LH', name: 'LUFTHANSA' },
    { code: 'EK', name: 'EMIRATES' },
    { code: 'AF', name: 'AIR FRANCE' },
    { code: 'SQ', name: 'SING<PERSON>ORE AIRLINES' },
    { code: 'XX', name: 'Unknown Airline' }, // Test fallback
    { code: null, name: 'No Code Airline' }  // Test null handling
  ];

  for (const airline of testAirlines) {
    try {
      const logoUrl = await airlineLogoService.getLogoForPDF(airline.code, airline.name);
      const isDataUrl = logoUrl.startsWith('data:image/');
      const isHttpUrl = logoUrl.startsWith('http');
      const isSvg = logoUrl.includes('svg');

      console.log(`  ✅ ${airline.code || 'NULL'} (${airline.name}): ${isDataUrl ? (isSvg ? 'SVG Generated' : 'PNG Data URL') : isHttpUrl ? 'External URL' : 'Unknown'}`);

      if (isDataUrl) {
        console.log(`    📏 Data URL length: ${logoUrl.length} characters`);
      }
    } catch (error) {
      console.log(`  ❌ ${airline.code || 'NULL'} (${airline.name}): Error - ${error.message}`);
    }
  }

  // Test 2: PDF generation with logos
  console.log('\n🎫 Testing PDF generation with airline logos:');

  const testTicketData = {
    tripDates: '21 JUL 2025 › 28 JUL 2025',
    destination: 'TRIP TO LEONARDO DA VINCI INTERNATIONAL AIRPORT',
    passengers: [
      { name: 'LOGO/TEST', surname: 'VERIFICATION/USER' }
    ],
    reservationCode: 'LOGO01',
    airlineReservationCode: 'TEST01',
    segments: [
      {
        airline: 'CATHAY PACIFIC',
        flightNo: 'CX 123',
        route: 'MAN – FCO',
        duration: '2H 55M',
        stops: 0,
        departure: {
          date: 'MONDAY, JUL 21',
          airport: 'MAN',
          airportName: 'Manchester Airport',
          time: '07:05',
          terminal: 'TERMINAL 1'
        },
        arrival: {
          airport: 'FCO',
          airportName: 'Leonardo da Vinci International Airport',
          time: '11:00',
          terminal: 'TERMINAL 1'
        },
        aircraft: 'BOEING 737-800',
        flightClass: 'Economy Class (M)',
        status: 'Confirmed'
      },
      {
        airline: 'BRITISH AIRWAYS',
        flightNo: 'BA 456',
        route: 'FCO – MAN',
        duration: '3H 5M',
        stops: 0,
        departure: {
          date: 'MONDAY, JUL 28',
          airport: 'FCO',
          airportName: 'Leonardo da Vinci International Airport',
          time: '12:00',
          terminal: 'TERMINAL 1'
        },
        arrival: {
          airport: 'MAN',
          airportName: 'Manchester Airport',
          time: '14:05',
          terminal: 'TERMINAL 1'
        },
        aircraft: 'AIRBUS A320',
        flightClass: 'Economy Class (M)',
        status: 'Confirmed'
      }
    ],
    showNotice: true,
    customNotice: "This is a test reservation for airline logo verification."
  };

  try {
    const outputPath = path.join(__dirname, 'test-outputs', 'airline-logo-test.pdf');

    // Ensure output directory exists
    const fs = require('fs').promises;
    const outputDir = path.dirname(outputPath);
    try {
      await fs.mkdir(outputDir, { recursive: true });
    } catch (err) {
      // Directory might already exist
    }

    console.log('  📄 Generating PDF with airline logos...');
    await pdfService.generatePDF(testTicketData, outputPath);

    console.log('  ✅ PDF generated successfully with airline logos');
    console.log(`  💾 Saved to: ${outputPath}`);

    // Open the PDF for visual inspection
    const { exec } = require('child_process');
    exec(`open "${outputPath}"`, (error) => {
      if (error) {
        console.log('  ⚠️  Could not auto-open PDF. Please open manually:', outputPath);
      }
    });

  } catch (error) {
    console.log('  ❌ PDF generation error:', error.message);
  } finally {
    await pdfService.cleanup();
  }

  // Test 3: Cache performance
  console.log('\n⚡ Testing logo cache performance:');

  const startTime = Date.now();
  const cacheTestPromises = [];

  for (let i = 0; i < 10; i++) {
    cacheTestPromises.push(airlineLogoService.getLogoForPDF('LH', 'LUFTHANSA'));
    cacheTestPromises.push(airlineLogoService.getLogoForPDF('BA', 'BRITISH AIRWAYS'));
    cacheTestPromises.push(airlineLogoService.getLogoForPDF('CX', 'CATHAY PACIFIC'));
  }

  await Promise.all(cacheTestPromises);
  const endTime = Date.now();

  console.log(`  ✅ 30 logo requests completed in ${endTime - startTime}ms`);
  console.log(`  📈 Average: ${((endTime - startTime) / 30).toFixed(2)}ms per request`);

  console.log('\n🎯 What to check in the generated PDF:');
  console.log('  • Each airline should display its authentic brand logo');
  console.log('  • Logos should be clear and properly sized (70px × 45px)');
  console.log('  • No generic placeholders or broken image icons');
  console.log('  • Logos should match the actual airline branding');
  console.log('  • CATHAY PACIFIC should show the real Cathay Pacific logo');
  console.log('  • BRITISH AIRWAYS should show the real British Airways logo');

  console.log('\n🏁 Airline logo testing completed!');
}

// Run tests if this file is executed directly
if (require.main === module) {
  testAirlineLogos().catch(console.error);
}

module.exports = { testAirlineLogos };
