<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VerifiedOnward - Embassy-Approved Flight Reservations</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .header {
            background: rgba(13, 27, 42, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
        }
        .tagline {
            color: #94a3b8;
            font-size: 0.9rem;
        }
        .main-container {
            max-width: 1000px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        .hero {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }
        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .hero p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        .card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .card h2 {
            color: #0D1B2A;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
        }
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #374151;
            font-size: 0.9rem;
        }
        input, select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.2s;
        }
        input:focus, select:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }
        .button-group {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin: 2rem 0;
        }
        button {
            background: linear-gradient(135deg, #6366F1, #7C3AED);
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.2s;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }
        button:active {
            transform: translateY(0);
        }
        .btn-secondary {
            background: linear-gradient(135deg, #64748b, #475569);
        }
        .result {
            margin-top: 1.5rem;
            padding: 1.5rem;
            border-radius: 15px;
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            animation: slideIn 0.3s ease-out;
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .error {
            background: #fef2f2;
            border-color: #ef4444;
            color: #dc2626;
        }
        .success {
            background: #f0fdf4;
            border-color: #22c55e;
            color: #16a34a;
        }
        .trust-badges {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin: 2rem 0;
            flex-wrap: wrap;
        }
        .trust-badge {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            color: white;
            font-size: 0.9rem;
        }
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        .status-online { background: #22c55e; }
        .status-offline { background: #ef4444; }
        .footer {
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 3rem;
            padding: 2rem;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div>
                <div class="logo">VerifiedOnward</div>
                <div class="tagline">Embassy-Approved Flight Reservations</div>
            </div>
            <div style="color: white; font-size: 0.9rem;">
                <span class="status-indicator" id="backendStatus"></span>
                <span id="backendStatusText">Checking...</span>
            </div>
        </div>
    </header>

    <div class="main-container">
        <div class="hero">
            <h1>🎯 Embassy-Ready Flight Reservations</h1>
            <p>Generate professional Cathay Pacific-style flight documents in 60 seconds</p>

            <div class="trust-badges">
                <div class="trust-badge">
                    <span>✅</span>
                    <span>Real airline data</span>
                </div>
                <div class="trust-badge">
                    <span>🏛️</span>
                    <span>Embassy-compliant PNR</span>
                </div>
                <div class="trust-badge">
                    <span>⚡</span>
                    <span>Instant delivery</span>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>🎫 Generate Flight Reservation</h2>

            <div class="form-grid">
                <div class="form-group full-width">
                    <label>Booking Reference:</label>
                    <input type="text" id="bookingRef" value="CATHAY001" placeholder="e.g., CATHAY001" />
                </div>

                <div class="form-group">
                    <label>Passenger 1 - First Name:</label>
                    <input type="text" id="firstName1" value="JANE" placeholder="First name" />
                </div>

                <div class="form-group">
                    <label>Passenger 1 - Last Name:</label>
                    <input type="text" id="lastName1" value="COOPER" placeholder="Last name" />
                </div>

                <div class="form-group">
                    <label>Passenger 2 - First Name (Optional):</label>
                    <input type="text" id="firstName2" value="JENNY" placeholder="First name" />
                </div>

                <div class="form-group">
                    <label>Passenger 2 - Last Name (Optional):</label>
                    <input type="text" id="lastName2" value="WILSON" placeholder="Last name" />
                </div>
            </div>

            <div class="button-group">
                <button onclick="generatePDF()">🎯 Generate Cathay Pacific PDF</button>
                <button onclick="downloadPDF()" class="btn-secondary">📥 Download PDF</button>
            </div>

            <div id="result"></div>
        </div>

        <div class="card">
            <h2>🔧 System Status</h2>
            <div class="button-group">
                <button onclick="testBackend()" class="btn-secondary">🔍 Test Backend Connection</button>
            </div>
        </div>
    </div>

    <footer class="footer">
        <p>VerifiedOnward - Professional flight reservations for visa applications</p>
        <p style="font-size: 0.8rem; margin-top: 0.5rem;">Backend API: http://localhost:5001</p>
    </footer>

    <script>
        let currentBookingRef = '';
        
        async function testBackend() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>🔍 Testing backend connection...</p>';
            
            try {
                const response = await fetch('http://localhost:5001/api/health');
                const data = await response.json();
                
                updateBackendStatus(true, 'Connected');
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h3>✅ Backend Connected!</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Message:</strong> ${data.message || 'OK'}</p>
                        <p><strong>Cathay PDF Service:</strong> Ready</p>
                    </div>
                `;
            } catch (error) {
                updateBackendStatus(false, 'Connection failed');
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Backend Connection Failed</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>Make sure the backend is running on port 5001</p>
                        <p><strong>Troubleshooting:</strong> Check if the server is started</p>
                    </div>
                `;
            }
        }
        
        async function generatePDF() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>🔄 Generating Cathay Pacific PDF...</p>';
            
            const bookingRef = document.getElementById('bookingRef').value;
            const firstName1 = document.getElementById('firstName1').value;
            const lastName1 = document.getElementById('lastName1').value;
            const firstName2 = document.getElementById('firstName2').value;
            const lastName2 = document.getElementById('lastName2').value;
            
            const requestData = {
                bookingReference: bookingRef,
                passengers: [
                    { firstName: firstName1, lastName: lastName1 }
                ],
                outboundFlight: {
                    airline: 'CATHAY PACIFIC',
                    flightNumber: 'CX 784',
                    departureDate: '2021-07-18',
                    departureTime: '16:05',
                    arrivalTime: '21:05',
                    duration: '05hr(s) 00min(s)',
                    aircraft: 'AIRBUS INDUSTRIE A330-300',
                    departureAirport: {
                        iata: 'DPS',
                        city: 'Denpasar-Bali',
                        country: 'Indonesia'
                    },
                    arrivalAirport: {
                        iata: 'HKG',
                        city: 'Hong Kong',
                        country: 'Hong Kong'
                    }
                },
                returnFlight: {
                    airline: 'CATHAY PACIFIC',
                    flightNumber: 'CX 844',
                    departureDate: '2021-07-19',
                    departureTime: '02:05',
                    arrivalTime: '06:00',
                    duration: '15hr(s) 55min(s)',
                    aircraft: 'BOEING 777-300ER',
                    departureAirport: {
                        iata: 'HKG',
                        city: 'Hong Kong',
                        country: 'Hong Kong'
                    },
                    arrivalAirport: {
                        iata: 'JFK',
                        city: 'New York',
                        country: 'United States Of America'
                    }
                }
            };
            
            if (firstName2 && lastName2) {
                requestData.passengers.push({ firstName: firstName2, lastName: lastName2 });
            }
            
            try {
                const response = await fetch('http://localhost:5001/api/tickets/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    currentBookingRef = bookingRef;
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ Cathay Pacific PDF Generated!</h3>
                            <p><strong>Booking Reference:</strong> ${bookingRef}</p>
                            <p><strong>Download URL:</strong> ${data.downloadUrl}</p>
                            <p>Click "Download PDF" to get your embassy-ready flight reservation!</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ PDF Generation Failed</h3>
                            <p><strong>Error:</strong> ${data.error}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Request Failed</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
        
        function downloadPDF() {
            document.getElementById('result').innerHTML = `
                <div class="result error">
                    <h3>⚠️ PDF Generation Disabled</h3>
                    <p>PDF generation is temporarily disabled. Please check back later.</p>
                </div>
            `;
        }
        
        function updateBackendStatus(isOnline, message) {
            const statusIndicator = document.getElementById('backendStatus');
            const statusText = document.getElementById('backendStatusText');

            if (isOnline) {
                statusIndicator.className = 'status-indicator status-online';
                statusText.textContent = 'Backend Online';
            } else {
                statusIndicator.className = 'status-indicator status-offline';
                statusText.textContent = 'Backend Offline';
            }
        }

        // Test backend connection on page load
        window.onload = function() {
            testBackend();
        };
    </script>
</body>
</html>
