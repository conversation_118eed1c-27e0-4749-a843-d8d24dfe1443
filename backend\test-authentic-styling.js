const pdfService = require('./services/pdfService');
const path = require('path');
const fs = require('fs').promises;

/**
 * Comprehensive test for authentic airline reservation styling
 * Tests all critical visual adjustments for 100% authenticity
 */
async function testAuthenticStyling() {
  console.log('🎯 Testing Authentic Airline Reservation Styling...\n');

  // Test data with multiple airlines to verify logo integration
  const testCases = [
    {
      name: 'Cathay Pacific - Hong Kong to New York',
      data: {
        tripDates: '18 JUL 2021 ► 19 JUL 2021',
        destination: 'TRIP TO NEW YORK CITY',
        passengers: [
          { name: 'COOPER/JANE MR.' },
          { name: 'WILSON/JENNY MR.' }
        ],
        reservationCode: 'NHG8IQ',
        airlineReservationCode: 'NHG8IQ',
        segments: [
          {
            airline: 'CATHAY PACIFIC',
            flightNo: 'CX 784',
            from: { code: 'DPS', city: 'Denpasar-Bali, Indonesia', time: '16:05', terminal: '1' },
            to: { code: 'HKG', city: 'Hong Kong, Hong Kong', time: '21:05', terminal: '1' },
            departureDay: 'SUNDAY 18 JUL',
            duration: '05hr(s) 00min(s)',
            flightClass: 'Economy Class (M)',
            aircraft: 'AIRBUS INDUSTRIE A330-300',
            distance: 'Not Available',
            stops: '0',
            meals: 'Not Available'
          },
          {
            airline: 'CATHAY PACIFIC',
            flightNo: 'CX 844',
            from: { code: 'HKG', city: 'Hong Kong, Hong Kong', time: '02:05', terminal: '1' },
            to: { code: 'JFK', city: 'New York, United States Of America', time: '06:00', terminal: '8' },
            departureDay: 'MONDAY 19 JUL',
            duration: '15hr(s) 55min(s)',
            flightClass: 'Economy Class (M)',
            aircraft: 'BOEING 777-300ER',
            distance: 'Not Available',
            stops: '0',
            meals: 'Not Available'
          }
        ],
        showNotice: false
      }
    },
    {
      name: 'EasyJet - Manchester to Malta',
      data: {
        tripDates: '28 JUL 2025',
        destination: 'TRIP TO MALTA INTERNATIONAL AIRPORT',
        passengers: [
          { name: 'MISRATI/HUDIFA MR.' }
        ],
        reservationCode: 'IDT1752693138034GKP6',
        airlineReservationCode: 'IDT1752693138034GKP6',
        segments: [
          {
            airline: 'EASYJET',
            flightNo: 'U2 2273',
            from: { code: 'MAN', city: 'Manchester Airport', time: '05:55', terminal: '1' },
            to: { code: 'MLA', city: 'Malta International Airport', time: '10:25', terminal: '1' },
            departureDay: 'MONDAY, JUL 28',
            duration: '3h 30m',
            flightClass: 'Economy Class (M)',
            aircraft: 'Boeing 737-800',
            distance: 'Not Available',
            stops: '0',
            meals: 'Not Available'
          }
        ],
        showNotice: false
      }
    },
    {
      name: 'British Airways - London to Dubai',
      data: {
        tripDates: '15 AUG 2025',
        destination: 'TRIP TO DUBAI INTERNATIONAL AIRPORT',
        passengers: [
          { name: 'SMITH/JOHN MR.' },
          { name: 'SMITH/JANE MRS.' }
        ],
        reservationCode: 'BA123456789',
        airlineReservationCode: 'BA123456789',
        segments: [
          {
            airline: 'BRITISH AIRWAYS',
            flightNo: 'BA 105',
            from: { code: 'LHR', city: 'London Heathrow', time: '14:30', terminal: '5' },
            to: { code: 'DXB', city: 'Dubai International', time: '23:45', terminal: '3' },
            departureDay: 'FRIDAY, AUG 15',
            duration: '7h 15m',
            flightClass: 'Economy Class (M)',
            aircraft: 'Boeing 777-300ER',
            distance: '3,414 miles',
            stops: '0',
            meals: 'Available'
          }
        ],
        showNotice: false
      }
    }
  ];

  // Generate test PDFs
  for (const testCase of testCases) {
    try {
      console.log(`📄 Generating: ${testCase.name}`);
      
      // Create output directory if it doesn't exist
      const outputDir = path.join(__dirname, 'test-outputs');
      try {
        await fs.mkdir(outputDir, { recursive: true });
      } catch (err) {
        // Directory already exists
      }

      const filename = testCase.name.toLowerCase().replace(/[^a-z0-9]/g, '-') + '.pdf';
      const outputPath = path.join(outputDir, filename);

      const pdfBuffer = await pdfService.generatePDF(testCase.data, outputPath);
      
      console.log(`  ✅ Generated: ${outputPath}`);
      console.log(`  📊 Size: ${(pdfBuffer.length / 1024).toFixed(1)} KB`);
      
    } catch (error) {
      console.log(`  ❌ Error generating ${testCase.name}: ${error.message}`);
    }
  }

  console.log('\n🔍 VALIDATION CHECKLIST:');
  console.log('[ ] Airline logo appears centered above airline name in left panel');
  console.log('[ ] "CONFIRMED" is black, normal weight, uppercase only');
  console.log('[ ] All table borders are 2px solid black');
  console.log('[ ] All headers and labels are UPPERCASE');
  console.log('[ ] Important Information section is removed');
  console.log('[ ] Solid black lines separate flight segments');
  console.log('[ ] All table cells have consistent 6px vertical padding');
  console.log('[ ] No colors used except for airline logos');
  console.log('[ ] Layout matches Onwardticket samples exactly');
  
  console.log('\n📋 Manual verification required:');
  console.log('1. Open generated PDFs in test-outputs/ directory');
  console.log('2. Compare against Onwardticket reference samples');
  console.log('3. Verify all checklist items are implemented');
  console.log('4. Check logo positioning and sizing');
  console.log('5. Confirm authentic airline reservation appearance');
}

// Run the test
if (require.main === module) {
  testAuthenticStyling().catch(console.error);
}

module.exports = { testAuthenticStyling };
