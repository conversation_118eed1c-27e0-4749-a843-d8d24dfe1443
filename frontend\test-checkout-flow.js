#!/usr/bin/env node

/**
 * Test script to simulate the checkout flow issue
 * This will help us identify the exact problem when navigating from Flight Summary to Checkout
 */

console.log('🧪 TESTING CHECKOUT FLOW ISSUE');
console.log('================================');

// Test 1: Check if the checkout page can be accessed directly
console.log('\n1️⃣ Testing direct checkout access...');

const testDirectCheckout = () => {
  try {
    console.log('✅ Direct checkout URL: http://localhost:5176/checkout');
    console.log('✅ Test mode URL: http://localhost:5176/checkout?test=true');
    return true;
  } catch (error) {
    console.error('❌ Direct checkout test failed:', error.message);
    return false;
  }
};

// Test 2: Check BookingContext structure
console.log('\n2️⃣ Testing BookingContext structure...');

const testBookingContext = () => {
  try {
    const fs = require('fs');
    const path = require('path');
    
    const contextFile = path.join(__dirname, 'src/context/BookingContext.jsx');
    const content = fs.readFileSync(contextFile, 'utf8');
    
    // Check for critical parts
    const hasProvider = content.includes('BookingProvider');
    const hasUseBooking = content.includes('export const useBooking');
    const hasErrorCheck = content.includes('useBooking must be used within a BookingProvider');
    
    console.log('✅ BookingProvider exists:', hasProvider);
    console.log('✅ useBooking hook exists:', hasUseBooking);
    console.log('✅ Error handling exists:', hasErrorCheck);
    
    return hasProvider && hasUseBooking && hasErrorCheck;
  } catch (error) {
    console.error('❌ BookingContext test failed:', error.message);
    return false;
  }
};

// Test 3: Check App.jsx wrapper
console.log('\n3️⃣ Testing App.jsx BookingProvider wrapper...');

const testAppWrapper = () => {
  try {
    const fs = require('fs');
    const path = require('path');
    
    const appFile = path.join(__dirname, 'src/App.jsx');
    const content = fs.readFileSync(appFile, 'utf8');
    
    const hasBookingProvider = content.includes('<BookingProvider>');
    const hasCheckoutRoute = content.includes('path="/checkout"');
    const hasCheckoutPageFixed = content.includes('CheckoutPageFixed');
    
    console.log('✅ BookingProvider wrapper:', hasBookingProvider);
    console.log('✅ Checkout route exists:', hasCheckoutRoute);
    console.log('✅ CheckoutPageFixed component:', hasCheckoutPageFixed);
    
    return hasBookingProvider && hasCheckoutRoute && hasCheckoutPageFixed;
  } catch (error) {
    console.error('❌ App wrapper test failed:', error.message);
    return false;
  }
};

// Test 4: Check CheckoutPageFixed error handling
console.log('\n4️⃣ Testing CheckoutPageFixed error handling...');

const testCheckoutErrorHandling = () => {
  try {
    const fs = require('fs');
    const path = require('path');
    
    const checkoutFile = path.join(__dirname, 'src/pages/CheckoutPageFixed.jsx');
    const content = fs.readFileSync(checkoutFile, 'utf8');
    
    const hasErrorBoundary = content.includes('CheckoutErrorBoundary');
    const hasUseBookingTryCatch = content.includes('try {') && content.includes('useBooking()');
    const hasFallbackData = content.includes('fallback');
    const hasContextError = content.includes('contextError');
    
    console.log('✅ Error boundary exists:', hasErrorBoundary);
    console.log('✅ useBooking try-catch:', hasUseBookingTryCatch);
    console.log('✅ Fallback data handling:', hasFallbackData);
    console.log('✅ Context error handling:', hasContextError);
    
    return hasErrorBoundary && hasUseBookingTryCatch;
  } catch (error) {
    console.error('❌ Checkout error handling test failed:', error.message);
    return false;
  }
};

// Test 5: Simulate navigation data
console.log('\n5️⃣ Testing navigation data simulation...');

const testNavigationData = () => {
  try {
    // Simulate the data that would be passed from FlightSummary to Checkout
    const mockCheckoutData = {
      selectedFlight: {
        id: 'test-flight-123',
        airline: 'Test Airlines',
        flightNumber: 'TA123',
        departure: {
          airport: 'JFK',
          time: '10:00',
          date: '2025-02-01'
        },
        arrival: {
          airport: 'LHR',
          time: '22:00',
          date: '2025-02-01'
        },
        price: 4.99
      },
      passengers: [
        { id: 1, firstName: 'John', lastName: 'Doe' }
      ],
      email: '<EMAIL>',
      tripType: 'oneWay'
    };
    
    console.log('✅ Mock checkout data structure valid');
    console.log('✅ Flight data:', mockCheckoutData.selectedFlight ? 'Present' : 'Missing');
    console.log('✅ Passenger data:', mockCheckoutData.passengers.length > 0 ? 'Present' : 'Missing');
    console.log('✅ Email data:', mockCheckoutData.email ? 'Present' : 'Missing');
    
    return true;
  } catch (error) {
    console.error('❌ Navigation data test failed:', error.message);
    return false;
  }
};

// Run all tests
const runAllTests = async () => {
  console.log('\n🚀 RUNNING ALL TESTS...');
  console.log('========================');
  
  const results = {
    directCheckout: testDirectCheckout(),
    bookingContext: testBookingContext(),
    appWrapper: testAppWrapper(),
    checkoutErrorHandling: testCheckoutErrorHandling(),
    navigationData: testNavigationData()
  };
  
  console.log('\n📊 TEST RESULTS:');
  console.log('=================');
  
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  const allPassed = Object.values(results).every(result => result === true);
  
  console.log('\n🎯 OVERALL RESULT:');
  console.log('==================');
  console.log(allPassed ? '🎉 ALL TESTS PASSED' : '❌ SOME TESTS FAILED');
  
  if (!allPassed) {
    console.log('\n🔧 RECOMMENDED ACTIONS:');
    console.log('=======================');
    console.log('1. Check browser console for specific error messages');
    console.log('2. Verify BookingProvider is properly wrapping the checkout route');
    console.log('3. Test the checkout page with ?test=true parameter');
    console.log('4. Check if useBooking hook is being called correctly');
    console.log('5. Verify error boundary is catching and displaying errors properly');
  }
  
  return allPassed;
};

// Execute tests
runAllTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('🚨 Test execution failed:', error);
  process.exit(1);
});
